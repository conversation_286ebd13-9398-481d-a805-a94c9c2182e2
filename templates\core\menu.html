{% extends "base.html" %}

{% block title %}{{ _('القائمة') }} - {{ super() }}{% endblock %}

{% block content %}
<div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8" 
     x-data="menuApp()" 
     x-init="init()">
    
    <!-- Header -->
    <div class="text-center mb-12">
        <h1 class="text-4xl font-bold text-gray-900 mb-4">{{ _('قائمة الطعام') }}</h1>
        <p class="text-xl text-gray-600">{{ _('اكتشف أشهى الأطباق والمشروبات') }}</p>
    </div>
    
    <!-- Search Bar -->
    <div class="mb-8">
        <div class="max-w-md mx-auto">
            <div class="relative">
                <input type="text" 
                       x-model="searchQuery"
                       @input="searchProducts()"
                       placeholder="{{ _('ابحث في القائمة...') }}"
                       class="w-full px-4 py-3 {{ 'pr-12' if current_lang == 'ar' else 'pl-12' }} border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary focus:border-transparent">
                <div class="absolute inset-y-0 {{ 'right-0 pr-3' if current_lang == 'ar' else 'left-0 pl-3' }} flex items-center">
                    <i class="fas fa-search text-gray-400"></i>
                </div>
            </div>
        </div>
    </div>
    
    <!-- Currency Selector (Admin Only) -->
    {% if current_user.is_authenticated %}
    <div class="mb-8 text-center">
        <div class="inline-flex rounded-lg border border-gray-200 bg-white p-1">
            <template x-for="(name, code) in currencies" :key="code">
                <button @click="setCurrency(code)"
                        :class="selectedCurrency === code ? 'bg-primary text-white' : 'text-gray-700 hover:bg-gray-100'"
                        class="px-4 py-2 text-sm font-medium rounded-md transition-colors"
                        x-text="code">
                </button>
            </template>
        </div>
    </div>
    {% endif %}
    
    <!-- Loading State -->
    <div x-show="loading" class="text-center py-12">
        <div class="inline-block animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
        <p class="mt-2 text-gray-600">{{ _('جاري التحميل...') }}</p>
    </div>
    
    <!-- Search Results -->
    <div x-show="searchQuery && searchResults.length > 0" class="mb-8">
        <h2 class="text-2xl font-bold text-gray-900 mb-6">{{ _('نتائج البحث') }}</h2>
        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            <template x-for="product in searchResults" :key="product.id">
                <div class="bg-white rounded-lg shadow-md overflow-hidden hover:shadow-lg transition-shadow">
                    <div class="aspect-w-16 aspect-h-9">
                        <img :src="product.photo_b64 || '/static/images/placeholder.jpg'" 
                             :alt="product.name"
                             class="w-full h-48 object-cover">
                    </div>
                    <div class="p-4">
                        <h3 class="text-lg font-semibold text-gray-900 mb-2" x-text="product.name"></h3>
                        <p class="text-gray-600 text-sm mb-3" x-text="product.description"></p>
                        <div class="flex justify-between items-center">
                            <span class="text-lg font-bold text-primary" 
                                  x-text="formatPrice(product.price, product.currency)"></span>
                            <button class="bg-secondary text-white px-4 py-2 rounded-md hover:bg-opacity-90 transition-colors">
                                {{ _('عرض التفاصيل') }}
                            </button>
                        </div>
                    </div>
                </div>
            </template>
        </div>
    </div>
    
    <!-- No Search Results -->
    <div x-show="searchQuery && searchResults.length === 0 && !loading" class="text-center py-12">
        <i class="fas fa-search text-4xl text-gray-400 mb-4"></i>
        <h3 class="text-xl font-semibold text-gray-900 mb-2">{{ _('لا توجد نتائج') }}</h3>
        <p class="text-gray-600">{{ _('لم نجد أي منتجات تطابق بحثك') }}</p>
    </div>
    
    <!-- Categories Menu -->
    <div x-show="!searchQuery" class="space-y-12">
        <template x-for="category in categories" :key="category.id">
            <div class="fade-in">
                <!-- Category Header -->
                <div class="flex items-center mb-6">
                    <div class="flex items-center">
                        <i :class="category.icon || 'fas fa-utensils'" 
                           class="text-2xl text-primary {{ 'ml-3' if current_lang != 'ar' else 'mr-3' }}"></i>
                        <h2 class="text-3xl font-bold text-gray-900" x-text="category.name"></h2>
                    </div>
                    <div class="flex-1 h-px bg-gray-200 {{ 'mr-6' if current_lang == 'ar' else 'ml-6' }}"></div>
                </div>
                
                <!-- Category Description -->
                <p x-show="category.description" 
                   class="text-gray-600 mb-6" 
                   x-text="category.description"></p>
                
                <!-- Subcategories -->
                <template x-for="subcategory in category.subcategories" :key="subcategory.id">
                    <div x-show="subcategory.products && subcategory.products.length > 0" class="mb-8">
                        <h3 class="text-xl font-semibold text-gray-800 mb-4" x-text="subcategory.name"></h3>
                        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                            <template x-for="product in subcategory.products" :key="product.id">
                                <div class="bg-white rounded-lg shadow-md overflow-hidden hover:shadow-lg transition-shadow cursor-pointer"
                                     @click="showProductModal(product)">
                                    <div class="aspect-w-16 aspect-h-9">
                                        <img :src="product.photo_b64 || '/static/images/placeholder.jpg'" 
                                             :alt="product.name"
                                             class="w-full h-48 object-cover">
                                    </div>
                                    <div class="p-4">
                                        <div class="flex justify-between items-start mb-2">
                                            <h4 class="text-lg font-semibold text-gray-900" x-text="product.name"></h4>
                                            <span x-show="product.is_featured" 
                                                  class="bg-yellow-100 text-yellow-800 text-xs px-2 py-1 rounded-full">
                                                {{ _('مميز') }}
                                            </span>
                                        </div>
                                        <p class="text-gray-600 text-sm mb-3 line-clamp-2" x-text="product.description"></p>
                                        <div class="flex justify-between items-center">
                                            <span class="text-lg font-bold text-primary" 
                                                  x-text="formatPrice(product.price, product.currency)"></span>
                                            <button class="bg-secondary text-white px-3 py-1 rounded-md text-sm hover:bg-opacity-90 transition-colors">
                                                {{ _('عرض') }}
                                            </button>
                                        </div>
                                    </div>
                                </div>
                            </template>
                        </div>
                    </div>
                </template>
                
                <!-- Direct Category Products (no subcategory) -->
                <div x-show="category.products && category.products.length > 0">
                    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                        <template x-for="product in category.products.filter(p => !p.subcategory_id)" :key="product.id">
                            <div class="bg-white rounded-lg shadow-md overflow-hidden hover:shadow-lg transition-shadow cursor-pointer"
                                 @click="showProductModal(product)">
                                <div class="aspect-w-16 aspect-h-9">
                                    <img :src="product.photo_b64 || '/static/images/placeholder.jpg'" 
                                         :alt="product.name"
                                         class="w-full h-48 object-cover">
                                </div>
                                <div class="p-4">
                                    <div class="flex justify-between items-start mb-2">
                                        <h4 class="text-lg font-semibold text-gray-900" x-text="product.name"></h4>
                                        <span x-show="product.is_featured" 
                                              class="bg-yellow-100 text-yellow-800 text-xs px-2 py-1 rounded-full">
                                            {{ _('مميز') }}
                                        </span>
                                    </div>
                                    <p class="text-gray-600 text-sm mb-3 line-clamp-2" x-text="product.description"></p>
                                    <div class="flex justify-between items-center">
                                        <span class="text-lg font-bold text-primary" 
                                              x-text="formatPrice(product.price, product.currency)"></span>
                                        <button class="bg-secondary text-white px-3 py-1 rounded-md text-sm hover:bg-opacity-90 transition-colors">
                                            {{ _('عرض') }}
                                        </button>
                                    </div>
                                </div>
                            </div>
                        </template>
                    </div>
                </div>
            </div>
        </template>
    </div>
    
    <!-- Product Modal -->
    <div x-show="showModal" 
         x-transition:enter="transition ease-out duration-300"
         x-transition:enter-start="opacity-0"
         x-transition:enter-end="opacity-100"
         x-transition:leave="transition ease-in duration-200"
         x-transition:leave-start="opacity-100"
         x-transition:leave-end="opacity-0"
         class="fixed inset-0 z-50 overflow-y-auto"
         @click="closeModal()">
        <div class="flex items-center justify-center min-h-screen pt-4 px-4 pb-20 text-center sm:block sm:p-0">
            <div class="fixed inset-0 bg-gray-500 bg-opacity-75 transition-opacity"></div>
            
            <div class="inline-block align-bottom bg-white rounded-lg text-{{ 'right' if current_lang == 'ar' else 'left' }} overflow-hidden shadow-xl transform transition-all sm:my-8 sm:align-middle sm:max-w-lg sm:w-full"
                 @click.stop>
                <template x-if="selectedProduct">
                    <div>
                        <div class="aspect-w-16 aspect-h-9">
                            <img :src="selectedProduct.photo_b64 || '/static/images/placeholder.jpg'" 
                                 :alt="selectedProduct.name"
                                 class="w-full h-64 object-cover">
                        </div>
                        <div class="p-6">
                            <div class="flex justify-between items-start mb-4">
                                <h3 class="text-2xl font-bold text-gray-900" x-text="selectedProduct.name"></h3>
                                <button @click="closeModal()" class="text-gray-400 hover:text-gray-600">
                                    <i class="fas fa-times text-xl"></i>
                                </button>
                            </div>
                            
                            <p class="text-gray-600 mb-4" x-text="selectedProduct.description"></p>
                            
                            <div x-show="selectedProduct.ingredients" class="mb-4">
                                <h4 class="font-semibold text-gray-900 mb-2">{{ _('المكونات') }}</h4>
                                <p class="text-gray-600 text-sm" x-text="selectedProduct.ingredients"></p>
                            </div>
                            
                            <div class="flex justify-between items-center">
                                <span class="text-2xl font-bold text-primary" 
                                      x-text="formatPrice(selectedProduct.price, selectedProduct.currency)"></span>
                                <span x-show="selectedProduct.is_featured" 
                                      class="bg-yellow-100 text-yellow-800 text-sm px-3 py-1 rounded-full">
                                    {{ _('منتج مميز') }}
                                </span>
                            </div>
                        </div>
                    </div>
                </template>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
// Menu App Alpine.js Component
function menuApp() {
    return {
        categories: [],
        searchQuery: '',
        searchResults: [],
        loading: true,
        showModal: false,
        selectedProduct: null,
        selectedCurrency: '{{ settings.default_currency }}',
        currencies: {% if current_user.is_authenticated %}{{ available_currencies | tojson }}{% else %}{}{% endif %},
        currentLang: '{{ current_lang }}',
        
        async init() {
            await this.loadMenu();
            this.loading = false;
        },
        
        async loadMenu() {
            try {
                const response = await fetch(`/api/menu?lang=${this.currentLang}`);
                const data = await response.json();
                if (data.success) {
                    this.categories = data.data;
                }
            } catch (error) {
                console.error('Error loading menu:', error);
            }
        },
        
        async searchProducts() {
            if (!this.searchQuery.trim()) {
                this.searchResults = [];
                return;
            }
            
            this.loading = true;
            try {
                const response = await fetch(`/api/search?q=${encodeURIComponent(this.searchQuery)}&lang=${this.currentLang}`);
                const data = await response.json();
                if (data.success) {
                    this.searchResults = data.data;
                }
            } catch (error) {
                console.error('Error searching products:', error);
            }
            this.loading = false;
        },
        
        formatPrice(price, currency) {
            // For non-admin users, always use the default currency
            const targetCurrency = {% if current_user.is_authenticated %}this.selectedCurrency{% else %}'{{ settings.default_currency }}'{% endif %};
            // Simple currency conversion (in real app, use exchange rates)
            let convertedPrice = parseFloat(price);

            return new Intl.NumberFormat(this.currentLang === 'ar' ? 'ar-EG' : 'en-US', {
                style: 'currency',
                currency: targetCurrency,
                minimumFractionDigits: 0,
                maximumFractionDigits: 2
            }).format(convertedPrice);
        },
        
        setCurrency(currency) {
            {% if current_user.is_authenticated %}
            this.selectedCurrency = currency;
            {% endif %}
        },
        
        showProductModal(product) {
            this.selectedProduct = product;
            this.showModal = true;
            document.body.style.overflow = 'hidden';
        },
        
        closeModal() {
            this.showModal = false;
            this.selectedProduct = null;
            document.body.style.overflow = 'auto';
        }
    }
}
</script>
{% endblock %}
